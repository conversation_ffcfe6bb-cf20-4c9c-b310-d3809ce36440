// API测试脚本
const http = require('http');

// 测试配置
const BASE_URL = 'http://localhost:3000';

// 发送HTTP请求的辅助函数
function makeRequest(options, data = null) {
    return new Promise((resolve, reject) => {
        const req = http.request(options, (res) => {
            let body = '';
            res.on('data', (chunk) => {
                body += chunk;
            });
            res.on('end', () => {
                try {
                    const jsonBody = JSON.parse(body);
                    resolve({
                        statusCode: res.statusCode,
                        headers: res.headers,
                        body: jsonBody
                    });
                } catch (e) {
                    resolve({
                        statusCode: res.statusCode,
                        headers: res.headers,
                        body: body
                    });
                }
            });
        });

        req.on('error', (err) => {
            reject(err);
        });

        if (data) {
            req.write(JSON.stringify(data));
        }
        req.end();
    });
}

// 测试函数
async function runTests() {
    console.log('🧪 开始API测试...\n');

    try {
        // 1. 测试首页
        console.log('1. 测试首页 GET /');
        const homeResponse = await makeRequest({
            hostname: 'localhost',
            port: 3000,
            path: '/',
            method: 'GET'
        });
        console.log(`   状态码: ${homeResponse.statusCode}`);
        console.log(`   响应: ${JSON.stringify(homeResponse.body, null, 2)}\n`);

        // 2. 测试健康检查
        console.log('2. 测试健康检查 GET /health');
        const healthResponse = await makeRequest({
            hostname: 'localhost',
            port: 3000,
            path: '/health',
            method: 'GET'
        });
        console.log(`   状态码: ${healthResponse.statusCode}`);
        console.log(`   响应: ${JSON.stringify(healthResponse.body, null, 2)}\n`);

        // 3. 测试获取用户列表
        console.log('3. 测试获取用户列表 GET /api/users');
        const usersResponse = await makeRequest({
            hostname: 'localhost',
            port: 3000,
            path: '/api/users',
            method: 'GET'
        });
        console.log(`   状态码: ${usersResponse.statusCode}`);
        console.log(`   响应: ${JSON.stringify(usersResponse.body, null, 2)}\n`);

        // 4. 测试获取指定用户
        console.log('4. 测试获取指定用户 GET /api/users/1');
        const userResponse = await makeRequest({
            hostname: 'localhost',
            port: 3000,
            path: '/api/users/1',
            method: 'GET'
        });
        console.log(`   状态码: ${userResponse.statusCode}`);
        console.log(`   响应: ${JSON.stringify(userResponse.body, null, 2)}\n`);

        // 5. 测试创建用户
        console.log('5. 测试创建用户 POST /api/users');
        const createUserResponse = await makeRequest({
            hostname: 'localhost',
            port: 3000,
            path: '/api/users',
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            }
        }, {
            name: '测试用户',
            email: '<EMAIL>'
        });
        console.log(`   状态码: ${createUserResponse.statusCode}`);
        console.log(`   响应: ${JSON.stringify(createUserResponse.body, null, 2)}\n`);

        // 6. 测试搜索功能
        console.log('6. 测试搜索功能 GET /api/search?q=测试&page=1&limit=5');
        const searchResponse = await makeRequest({
            hostname: 'localhost',
            port: 3000,
            path: '/api/search?q=测试&page=1&limit=5',
            method: 'GET'
        });
        console.log(`   状态码: ${searchResponse.statusCode}`);
        console.log(`   响应: ${JSON.stringify(searchResponse.body, null, 2)}\n`);

        // 7. 测试404错误
        console.log('7. 测试404错误 GET /nonexistent');
        const notFoundResponse = await makeRequest({
            hostname: 'localhost',
            port: 3000,
            path: '/nonexistent',
            method: 'GET'
        });
        console.log(`   状态码: ${notFoundResponse.statusCode}`);
        console.log(`   响应: ${JSON.stringify(notFoundResponse.body, null, 2)}\n`);

        console.log('✅ 所有测试完成！');

    } catch (error) {
        console.error('❌ 测试过程中出现错误:', error.message);
    }
}

// 运行测试
if (require.main === module) {
    runTests();
}

module.exports = { runTests, makeRequest };
