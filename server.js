const express = require('express');
const app = express();

// 配置端口，默认为3000，可通过环境变量PORT设置
const PORT = process.env.PORT || 3000;

// 中间件：解析JSON请求体
app.use(express.json());

// 中间件：解析URL编码的请求体
app.use(express.urlencoded({ extended: true }));

// 中间件：记录请求日志
app.use((req, res, next) => {
    const timestamp = new Date().toISOString();
    console.log(`[${timestamp}] ${req.method} ${req.url} - IP: ${req.ip}`);
    next();
});

// 根路径处理
app.get('/', (req, res) => {
    res.json({
        message: 'Node.js Web服务器运行中',
        timestamp: new Date().toISOString(),
        version: '1.0.0'
    });
});


// POST请求示例
app.post('/api/users', (req, res) => {
    const { name, email } = req.body;
    
    if (!name || !email) {
        return res.status(400).json({
            success: false,
            message: '姓名和邮箱是必填项'
        });
    }
    
    // 模拟创建用户
    const newUser = {
        id: Date.now(), // 简单的ID生成
        name,
        email,
        createdAt: new Date().toISOString()
    };
    
    res.status(201).json({
        success: true,
        message: '用户创建成功',
        data: newUser
    });
});


// 错误处理中间件
app.use((err, req, res, next) => {
    console.error('服务器错误:', err.stack);
    res.status(500).json({
        success: false,
        message: '服务器内部错误',
        error: process.env.NODE_ENV === 'development' ? err.message : '请联系管理员'
    });
});

// 404处理
app.use((req, res) => {
    res.status(404).json({
        success: false,
        message: '请求的资源不存在',
        path: req.originalUrl
    });
});

// 启动服务器
app.listen(PORT, () => {
    console.log(`🚀 服务器启动成功！`);
    console.log(`📍 服务地址: http://localhost:${PORT}`);
    console.log(`🕐 启动时间: ${new Date().toISOString()}`);
    console.log(`📝 可用端点:`);
    console.log(`   GET  /              - 首页`);
    console.log(`   GET  /health        - 健康检查`);
    console.log(`   GET  /api/users     - 获取用户列表`);
    console.log(`   GET  /api/users/:id - 获取指定用户`);
    console.log(`   POST /api/users     - 创建用户`);
    console.log(`   PUT  /api/users/:id - 更新用户`);
    console.log(`   DELETE /api/users/:id - 删除用户`);
    console.log(`   GET  /api/search    - 搜索功能`);
});

// 优雅关闭
process.on('SIGTERM', () => {
    console.log('收到SIGTERM信号，正在关闭服务器...');
    process.exit(0);
});

process.on('SIGINT', () => {
    console.log('收到SIGINT信号，正在关闭服务器...');
    process.exit(0);
});
