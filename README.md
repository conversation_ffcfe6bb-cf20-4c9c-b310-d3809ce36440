# Node.js Web服务端

这是一个使用Node.js和Express框架构建的web服务端，用于处理HTTP请求并作出响应。

## 功能特性

- ✅ RESTful API设计
- ✅ JSON数据处理
- ✅ 请求日志记录
- ✅ 错误处理
- ✅ 健康检查端点
- ✅ 优雅关闭
- ✅ 环境变量配置

## 安装和运行

### 1. 安装依赖
```bash
npm install
```

### 2. 启动服务器
```bash
# 生产环境
npm start

# 开发环境
npm run dev
```

服务器默认运行在 `http://localhost:3000`

### 3. 自定义端口
```bash
# 设置环境变量
PORT=8080 npm start
```

## API端点

### 基础端点

| 方法 | 路径 | 描述 |
|------|------|------|
| GET | `/` | 首页，返回服务器信息 |
| GET | `/health` | 健康检查 |

### 用户管理API

| 方法 | 路径 | 描述 | 请求体 |
|------|------|------|--------|
| GET | `/api/users` | 获取用户列表 | - |
| GET | `/api/users/:id` | 获取指定用户 | - |
| POST | `/api/users` | 创建新用户 | `{"name": "姓名", "email": "邮箱"}` |
| PUT | `/api/users/:id` | 更新用户信息 | `{"name": "姓名", "email": "邮箱"}` |
| DELETE | `/api/users/:id` | 删除用户 | - |

### 搜索API

| 方法 | 路径 | 描述 | 查询参数 |
|------|------|------|----------|
| GET | `/api/search` | 搜索功能 | `q` (必填), `page`, `limit` |

## 使用示例

### 1. 获取服务器信息
```bash
curl http://localhost:3000/
```

### 2. 健康检查
```bash
curl http://localhost:3000/health
```

### 3. 获取用户列表
```bash
curl http://localhost:3000/api/users
```

### 4. 获取指定用户
```bash
curl http://localhost:3000/api/users/1
```

### 5. 创建新用户
```bash
curl -X POST http://localhost:3000/api/users \
  -H "Content-Type: application/json" \
  -d '{"name": "张三", "email": "<EMAIL>"}'
```

### 6. 更新用户信息
```bash
curl -X PUT http://localhost:3000/api/users/1 \
  -H "Content-Type: application/json" \
  -d '{"name": "李四", "email": "<EMAIL>"}'
```

### 7. 删除用户
```bash
curl -X DELETE http://localhost:3000/api/users/1
```

### 8. 搜索功能
```bash
curl "http://localhost:3000/api/search?q=关键词&page=1&limit=10"
```

## 响应格式

所有API响应都采用统一的JSON格式：

### 成功响应
```json
{
  "success": true,
  "data": {...},
  "message": "操作成功"
}
```

### 错误响应
```json
{
  "success": false,
  "message": "错误信息",
  "error": "详细错误信息"
}
```

## 项目结构

```
nodeService/
├── server.js          # 主服务器文件
├── package.json       # 项目配置文件
├── package-lock.json  # 依赖锁定文件
└── README.md         # 项目说明文档
```

## 扩展功能

如需添加更多功能，可以考虑：

1. **数据库集成**: 使用MongoDB、MySQL或PostgreSQL
2. **身份验证**: 添加JWT或Session认证
3. **文件上传**: 使用multer中间件
4. **API文档**: 集成Swagger/OpenAPI
5. **日志系统**: 使用winston或morgan
6. **缓存**: 集成Redis
7. **测试**: 添加Jest或Mocha测试框架

## 环境变量

| 变量名 | 描述 | 默认值 |
|--------|------|--------|
| PORT | 服务器端口 | 3000 |
| NODE_ENV | 运行环境 | development |

## 注意事项

- 确保Node.js版本 >= 14.0.0
- 生产环境建议使用PM2或其他进程管理器
- 建议配置反向代理（如Nginx）
- 定期更新依赖包以确保安全性
